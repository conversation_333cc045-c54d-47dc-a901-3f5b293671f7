#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Thu Oct  5 11:09:36 2023

@author: jinani

This code is a working example of estimating the receptive fields 
of a V1 neuron recorded in cats for the fist pass-without any cropping. 
First part of the  code loads the cell responses for natural images and 
carryout different preprocessing steps. Second part of the code implements 
the 2-pass ConvNet to estimate the model parameters.

Required functions can be found in /SysIden_AvgMethod_SupportFiles/k_functions.py
Custom layers for gaussian map layer and power law exponent is defined in /SysIden_AvgMethod_SupportFiles/k_layers.py
Implementation of the model with defined layers can be found in /SysIden_AvgMethod_SupportFiles/k_model.py

"""

# Import Required Libraries
import os
os.environ['KMP_DUPLICATE_LIB_OK']='True'  
from IPython import get_ipython
# get_ipython().magic('reset -sf') # To Clear Variables Before Script Runs
import h5py
import scipy.io as sio
import numpy as np
from tensorflow import keras
import matplotlib.pyplot as plt
from tensorflow.keras.callbacks import ModelCheckpoint
import scipy
import sys

# Import Custom Written Functions and Model
sys.path.insert(0,'D:\code\CNNwithPReLU_RFestimateV1\Real V1 Neuron\SysIden_AvgMethod_SupportFiles') # path to the support files folder
from SysIden_AvgMethod_SupportFiles.k_functions import arrange_stimuli, arrange_responses,conv_output_length,plotGaussMap,plotReconstruction
from SysIden_AvgMethod_SupportFiles.k_model import model_pass1, model_pass2


# INPUT DETAILS HERE !
dataset_path= 'D:\\code\\CNNwithPReLU_RFestimateV1\\Custom_Data_Analysis\\example_data\\PLCATD001_Ephys_DA07_Image_0\\'
dataset_name='PLCATD001_Ephys_DA07_Image_0'
crop_x1=0
crop_x2=64
crop_y1=0
crop_y2=64
kernel_size = int((crop_x2-crop_x1)/30)
num_timelags=7
Stride=1
Filter_Size=32
Pool_Size=1


# Arrange the Stimuli Dataset
Stimuli_dataset = sio.loadmat('D:\\code\\CNNwithPReLU_RFestimateV1\\Custom_Data_Analysis\\example_data\\PLCATD001_Ephys_DA07_Image_0\\stimuliSet.mat')
# mat_path = 'D:\\code\\CNNwithPReLU_RFestimateV1\\Custom_Data_Analysis\\example_data\\PLCATD001_Ephys_DA07_Image_0\\stimuliSet.mat'
# with h5py.File(mat_path, 'r') as f:
#     # 查看文件中包含的变量名
#     print(list(f.keys()))
#
#     # 例如你要读取名为 'stimuli' 的变量（根据你.mat里的变量名而定）
#     Stimuli_dataset = f['Stimuli_dataset'][:]
estSet,regSet,predSet,imSize=arrange_stimuli(Stimuli_dataset,crop_y1,crop_y2,crop_x1,crop_x2,kernel_size,num_timelags)

# Arrange the Response Dataset
y_est,y_reg,y_pred,num_neurons=arrange_responses(dataset_path,dataset_name)


# Calculate Shape of the Main Input and Intermediate Layer Inputs
Input_Shape=estSet.shape[1:]
numRows =  Input_Shape[0]
numCols =  Input_Shape[1]
assert numRows == numCols
numFrames = Input_Shape[2]

convImageSize = conv_output_length(numRows,Filter_Size,'valid',Stride) # Input to Conv2D Layer
downsampImageSize = conv_output_length(convImageSize,Pool_Size,'valid',Pool_Size) # Input to Gaussian Map Layer

# Model for Pass 1 : Filter Estimate Pass
Initial_Gaussian_Mean = np.array([0.5, 0.5], dtype='float32')
Initial_Gaussian_Sigma = np.array([1.0, 0.0, 1.0], dtype='float32')
model=model_pass1(Input_Shape,Filter_Size,Stride,Pool_Size,downsampImageSize)
model.summary()
optimizerFunction = keras.optimizers.Adam(lr=0.001)
model.compile(loss='mse', optimizer=optimizerFunction)
earlyStop=keras.callbacks.EarlyStopping(monitor='val_loss', patience=20, verbose=0, mode='auto')
mc = ModelCheckpoint('best_model_pass1.keras', monitor='val_loss', mode='min', verbose=1)
history=model.fit(estSet,y_est, validation_data=(regSet,y_reg), epochs = 500,
              batch_size=750,callbacks=[earlyStop,mc],verbose=1)

# Pass 1 Trained Weights
weights = model.get_weights()

# Initialize Filter Weights for Second Pass
Initial_Filter_Weights=[weights[0],weights[1]] # Receptive Field Estimates from Pass 1
Initial_exp=np.asarray([1]) # Intialize Power Law Exponet to 1

# Model for Pass 2 : Power Law Pass
model2=model_pass2(Input_Shape,Filter_Size,Stride,Pool_Size,downsampImageSize,Initial_Filter_Weights,Initial_exp)
model2.summary()
optimizerFunction = keras.optimizers.Adam(lr=0.001)
model2.compile(loss='mse', optimizer=optimizerFunction)
earlyStop=keras.callbacks.EarlyStopping(monitor='val_loss', patience=20, verbose=0, mode='auto')
mc = ModelCheckpoint('best_model_pass2.keras', monitor='val_loss', mode='min', verbose=1)
history=model2.fit(estSet,y_est, validation_data=(regSet,y_reg), epochs = 500,
              batch_size=750,callbacks=[earlyStop,mc],verbose=1)


# Calculate VAF
predicted_test_response = model2.predict(predSet)
predicted_test_response = predicted_test_response.reshape(-1)
respTest=y_pred.reshape(-1)
R=np.corrcoef(predicted_test_response,respTest)
diag=R[0,1]
VAF_test=diag*diag*100
print (VAF_test)

# Pass 2 Trained Weights
weights2 = model2.get_weights()

# Create Results directory
results_dir = os.path.join(dataset_path, 'Results')
os.makedirs(results_dir, exist_ok=True)
print(f"Results will be saved to: {results_dir}")

# Plot Results
# 1. Learning Curve
plt.figure()
plt.plot(history.history['loss'])
plt.plot(history.history['val_loss'])
plt.title('Learning Curve')
plt.ylabel('loss')
plt.xlabel('epoch')
plt.legend(['train', 'validation'], loc='upper right')
plt.grid()
plt.savefig(os.path.join(results_dir, 'Learning_Curve.png'), dpi=300, bbox_inches='tight')
plt.show()

# 2. Actual/Predicted Response Data
plt.figure()
plt.plot(respTest[0:100],color='r',label='Actual')
plt.plot(predicted_test_response[0:100],color='b',label='Estimated')
plt.legend(loc='upper right')
plt.grid()
plt.title("Response Data")
plt.savefig(os.path.join(results_dir, 'Response_Data_Comparison.png'), dpi=300, bbox_inches='tight')
plt.show()

# 3. PReLU
plt.figure()
alpha1 = np.squeeze(weights2[2])
x = np.arange(-100,101)
y = np.arange(-100,101)
y[y<=0] = alpha1*y[y<=0]
plt.plot(x,y)
plt.title('PReLU, alpha = {}'.format(np.round(alpha1,2)))
plt.savefig(os.path.join(results_dir, 'PReLU_Activation.png'), dpi=300, bbox_inches='tight')
plt.show()

# 4. Gaussian Map
plt.figure()
mapMean = weights2[3]
mapSigma = weights2[4]
mapVals = plotGaussMap(mapMean,mapSigma,downsampImageSize)
plt.title('Gaussian Map')
plt.savefig(os.path.join(results_dir, 'Gaussian_Map.png'), dpi=300, bbox_inches='tight')
plt.show()

print(f"Processing {num_neurons} neurons")

# 5. Receptive Field Filter Weights
# Note: The current model trains a single filter for all neurons
# The filter represents the common receptive field pattern
filterWeights = weights[0][:,:,:,0]
numFrames = filterWeights.shape[2]
vabs = np.abs(filterWeights)
vabs_max = np.max(vabs)

print(f"Filter weights shape: {filterWeights.shape}")
print(f"This single filter was trained on responses from {num_neurons} neurons")

# Create filter visualization
plt.figure(figsize=(numFrames*3, 3))
for frame_idx in range(numFrames):
    plt.subplot(1, numFrames, frame_idx+1)
    plt.imshow(filterWeights[:,:,frame_idx], vmin=-vabs_max, vmax=+vabs_max, cmap='RdBu_r')
    plt.title(f'Frame {frame_idx}')
    plt.axis('off')
    plt.colorbar(fraction=0.046, pad=0.04)

plt.suptitle(f'Receptive Field Filter Weights (Trained on {num_neurons} neurons)')
plt.tight_layout()
plt.savefig(os.path.join(results_dir, 'RF_FilterWeights.png'), dpi=300, bbox_inches='tight')
plt.show()

# 6. Reconstructed Receptive Field Filter
reconFilter = plotReconstruction(filterWeights, mapVals, Stride, Pool_Size, imSize[0])

plt.figure(figsize=(8, 8))
plt.imshow(reconFilter, cmap='RdBu_r')
plt.title(f'Reconstructed Receptive Field (Trained on {num_neurons} neurons)')
plt.axis('off')
plt.colorbar(fraction=0.046, pad=0.04)
plt.tight_layout()
plt.savefig(os.path.join(results_dir, 'RF_Reconstruction.png'), dpi=300, bbox_inches='tight')
plt.show()

# 7. Neuron Response Statistics
plt.figure(figsize=(12, 8))

# Plot response distribution for each dataset
plt.subplot(2, 2, 1)
plt.hist(y_est, bins=50, alpha=0.7, label='Training')
plt.xlabel('Response')
plt.ylabel('Frequency')
plt.title('Training Response Distribution')
plt.legend()

plt.subplot(2, 2, 2)
plt.hist(y_reg, bins=50, alpha=0.7, label='Validation', color='orange')
plt.xlabel('Response')
plt.ylabel('Frequency')
plt.title('Validation Response Distribution')
plt.legend()

plt.subplot(2, 2, 3)
plt.hist(y_pred, bins=50, alpha=0.7, label='Test', color='green')
plt.xlabel('Response')
plt.ylabel('Frequency')
plt.title('Test Response Distribution')
plt.legend()

plt.subplot(2, 2, 4)
# Summary statistics
stats_text = f"""
Dataset Statistics:
• Number of neurons: {num_neurons}
• Training samples: {len(y_est)}
• Validation samples: {len(y_reg)}
• Test samples: {len(y_pred)}

Response Statistics:
• Train mean: {np.mean(y_est):.3f}
• Train std: {np.std(y_est):.3f}
• Val mean: {np.mean(y_reg):.3f}
• Val std: {np.std(y_reg):.3f}
• Test mean: {np.mean(y_pred):.3f}
• Test std: {np.std(y_pred):.3f}

Model Performance:
• VAF: {VAF_test:.2f}%
"""
plt.text(0.1, 0.5, stats_text, fontsize=10, verticalalignment='center')
plt.axis('off')
plt.title('Dataset Summary')

plt.tight_layout()
plt.savefig(os.path.join(results_dir, 'Dataset_Statistics.png'), dpi=300, bbox_inches='tight')
plt.show()

# Save results in a .mat file in the Results directory
results_mat_file = os.path.join(results_dir, 'Results_AvgMethod.mat')
scipy.io.savemat(results_mat_file, {
    'weights_pass1': weights,
    'weights_pass2': weights2,
    'Final_Rf_Construst': reconFilter,
    'VAF': VAF_test,
    'Predicted_response': predicted_test_response,
    'num_neurons': num_neurons,
    'dataset_path': dataset_path,
    'dataset_name': dataset_name
})

print(f"Results saved to: {results_mat_file}")
