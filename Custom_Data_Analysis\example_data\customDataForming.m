clc;clear;
% 设置路径
kilosort_matfile = 'PLCATD001_Ephys_DA07_Image_0.mat';
stimuli_img_file = 'Lum_n24000_sizedeg(3, 3)_sizepx(64, 64)_pc(5, 95)_sd(0.2, 0.35)(1).mat';

% 创建保存目录
[~, fname, ~] = fileparts(kilosort_matfile);
save_dir = fname;
if ~exist(save_dir, 'dir')
    mkdir(save_dir);
end

%% Step 1: 加载数据
load(kilosort_matfile, 'spike0_kilosort3', 'ex');
load(stimuli_img_file, 'imgset');  % size = 24000 × 64 × 64

spike_times = spike0_kilosort3.time;
spike_templates = spike0_kilosort3.template;

stim_ids = ex.CondTest.CondIndex;      % 1~M，每次刺激的图像 index
stim_ons = ex.CondTest.CondOn;
stim_offs = ex.CondTest.CondOff;

unique_img_ids = unique(stim_ids);     % e.g. 1:4000，如果图像重复播放过
n_unique = numel(unique_img_ids);
n_neurons = max(spike_templates);

fprintf('发现 %d 个唯一图像，每个图像将平均多个试次...\n', n_unique);

%% Step 2: 为每张唯一图像计算平均firing rate
avg_resp_mat = zeros(n_unique, n_neurons);  % M x N

for i = 1:n_unique
    img_id = unique_img_ids(i);

    % 找到所有播放该图像的索引
    trials = find(stim_ids == img_id);
    n_repeats = numel(trials);

    resp_each_trial = zeros(n_repeats, n_neurons);

    for j = 1:n_repeats
        t_on = stim_ons(trials(j));
        t_off = stim_offs(trials(j));

        spikes_in_window = (spike_times >= t_on) & (spike_times < t_off);
        spike_templates_in_window = spike_templates(spikes_in_window);

        for nid = 1:n_neurons
            resp_each_trial(j, nid) = sum(spike_templates_in_window == nid) / (t_off - t_on);
        end
    end

    % 平均多个重复试次的响应
    avg_resp_mat(i, :) = mean(resp_each_trial, 1) * 1000; % ms -> s转换
end

%% Step 3: 对 imgset 进行图像抽取与划分
% imgset = 24000 × 64 × 64，对应 stim_ids 为 24000 条；
% 我们现在只保留 unique_img_ids 对应的图像即可

img_cells = imgset(unique_img_ids);   % n_unique × 1 cell，每个是 64×64 double

% 转换为 64×64×n 图像张量，并用 im2uint8 归一化
H = size(img_cells{1}, 1);
W = size(img_cells{1}, 2);
imgset_array = zeros(H, W, n_unique, 'uint8');

for i = 1:n_unique
    imgset_array(:, :, i) = im2uint8(img_cells{i});
end
%%
% 随机划分索引
idx = randperm(n_unique);
n_test = floor(n_unique * 1/6);
n_valid = floor(n_unique * 1/6);
n_train = n_unique - n_test - n_valid;

test_idx = idx(1:n_test);
valid_idx = idx(n_test+1:n_test+n_valid);
train_idx = idx(n_test+n_valid+1:end);

% 划分图像数据 (H×W×N)
Testing_stimuli = imgset_array(:, :, test_idx);
Validation_stimuli = imgset_array(:, :, valid_idx);
Training_stimuli = imgset_array(:, :, train_idx);

% 划分神经元响应
pred_Resp = avg_resp_mat(test_idx, :);
est_Resp = avg_resp_mat(valid_idx, :);
reg_Resp = avg_resp_mat(train_idx, :);
%%
% 保存图像数据
stimuli_file = fullfile(save_dir, 'stimuliSet.mat');
save(stimuli_file, 'Training_stimuli', 'Validation_stimuli', 'Testing_stimuli', '-v7');
%%
% 保存对应响应
save(fullfile(save_dir, 'PLCATD001_Ephys_DA07_Image_0_predSetResp.mat'), 'pred_Resp', '-v7');
save(fullfile(save_dir, 'PLCATD001_Ephys_DA07_Image_0_estSetResp.mat'), 'est_Resp', '-v7');
save(fullfile(save_dir, 'PLCATD001_Ephys_DA07_Image_0_regSetResp.mat'), 'reg_Resp', '-v7');

fprintf('✅ 图像数据已从 cell 提取并标准化为 uint8 格式，响应匹配完毕。\n');