#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration file for custom RF estimation

Modify these parameters according to your data and experimental setup.
"""

import os

# =============================================================================
# DATA PATHS AND FILES
# =============================================================================

# Data directory
DATA_DIR = "D:\CNNwithPReLU_RFestimateV1-main\Custom_Data_Analysis\example_data"

# Image data file (contains 'imgset' variable)
IMAGE_FILE = os.path.join(DATA_DIR, "Lum_n24000_sizedeg(3, 3)_sizepx(64, 64)_pc(5, 95)_sd(0.2, 0.35)(1).mat")

# Kilosort results file
KILOSORT_FILE = os.path.join(DATA_DIR, "PLCATD001_Ephys_DA07_Image_0.mat")

# Results output directory
RESULTS_DIR = "Results"

# =============================================================================
# NEURON SELECTION
# =============================================================================

# Which neuron to analyze (template ID from kilosort)
NEURON_ID = 0

# Alternative: analyze multiple neurons
NEURON_IDS = [0, 1, 2, 3, 4]  # List of neuron IDs to analyze
ANALYZE_MULTIPLE = False       # Set to True to analyze multiple neurons

# =============================================================================
# EXPERIMENTAL PARAMETERS
# =============================================================================

# Stimulus presentation parameters
STIMULUS_DURATION = 1.0      # Duration of each stimulus presentation (seconds)
RESPONSE_WINDOW = 0.5        # Time window to count spikes after stimulus onset (seconds)
BASELINE_WINDOW = 0.2        # Baseline window before stimulus onset (seconds)

# Image parameters
EXPECTED_IMAGE_SIZE = (64, 64)  # Expected image dimensions
IMAGE_DTYPE = 'float32'         # Data type for images

# =============================================================================
# MODEL ARCHITECTURE PARAMETERS
# =============================================================================

# Convolutional layer parameters
FILTER_SIZE = 15             # Size of the convolutional filter (adjust based on RF size)
STRIDE = 1                   # Convolution stride
N_KERNELS = 1               # Number of convolutional kernels

# Temporal dynamics
N_FRAMES = 4                # Number of temporal frames
FRAME_INDICES = list(range(N_FRAMES))  # Which frames to use

# PReLU initialization
INITIAL_PRELU_ALPHA = 0.5   # Initial value for PReLU alpha parameter

# Gaussian map layer parameters
INITIAL_GAUSSIAN_MEAN = None    # None for automatic initialization
INITIAL_GAUSSIAN_SIGMA = None   # None for automatic initialization

# =============================================================================
# TRAINING PARAMETERS
# =============================================================================

# Data splitting ratios
TRAIN_RATIO = 0.8           # 80% for training
VALIDATION_RATIO = 0.1      # 10% for validation  
TEST_RATIO = 0.1            # 10% for testing

# Training hyperparameters
LEARNING_RATE = 0.005       # Learning rate for Adam optimizer
BATCH_SIZE = 750            # Batch size (will be adjusted if dataset is smaller)
MAX_EPOCHS = 200            # Maximum number of training epochs
EARLY_STOP_PATIENCE = 20    # Early stopping patience

# Model checkpointing
SAVE_BEST_MODEL = True      # Save best model during training
MONITOR_METRIC = 'val_loss' # Metric to monitor for best model

# =============================================================================
# PREPROCESSING PARAMETERS
# =============================================================================

# Normalization parameters
NORMALIZE_IMAGES = True     # Whether to normalize image data
NORMALIZE_RESPONSES = True  # Whether to normalize neural responses

# Data augmentation (future extension)
USE_DATA_AUGMENTATION = False
AUGMENTATION_PARAMS = {
    'rotation_range': 5,
    'width_shift_range': 0.1,
    'height_shift_range': 0.1,
    'horizontal_flip': False,
    'vertical_flip': False
}

# =============================================================================
# VISUALIZATION PARAMETERS
# =============================================================================

# Figure parameters
FIGURE_DPI = 300            # DPI for saved figures
FIGURE_FORMAT = 'png'       # Format for saved figures
COLORMAP_RF = 'RdBu_r'     # Colormap for RF visualization
COLORMAP_GAUSS = 'viridis'  # Colormap for Gaussian map

# Plot settings
SHOW_PLOTS = True           # Whether to display plots
SAVE_PLOTS = True           # Whether to save plots
PLOT_STYLE = 'seaborn'      # Matplotlib style

# =============================================================================
# ANALYSIS PARAMETERS
# =============================================================================

# Performance metrics to compute
COMPUTE_CORRELATION = True
COMPUTE_MSE = True
COMPUTE_R_SQUARED = True
COMPUTE_EXPLAINED_VARIANCE = True

# Cross-validation parameters (future extension)
USE_CROSS_VALIDATION = False
CV_FOLDS = 5

# Statistical testing parameters
SIGNIFICANCE_LEVEL = 0.05
BOOTSTRAP_ITERATIONS = 1000

# =============================================================================
# COMPUTATIONAL PARAMETERS
# =============================================================================

# Memory management
MAX_MEMORY_USAGE = 0.8      # Maximum fraction of available memory to use
BATCH_SIZE_AUTO_ADJUST = True  # Automatically adjust batch size based on memory

# Parallel processing
USE_MULTIPROCESSING = False  # Whether to use multiprocessing
N_PROCESSES = 4             # Number of processes for multiprocessing

# GPU settings
USE_GPU = True              # Whether to use GPU if available
GPU_MEMORY_GROWTH = True    # Allow GPU memory growth
GPU_MEMORY_LIMIT = None     # GPU memory limit in MB (None for no limit)

# =============================================================================
# LOGGING AND DEBUGGING
# =============================================================================

# Logging parameters
LOG_LEVEL = 'INFO'          # Logging level: DEBUG, INFO, WARNING, ERROR
LOG_TO_FILE = True          # Whether to save logs to file
LOG_FILE = os.path.join(RESULTS_DIR, 'training.log')

# Debugging parameters
DEBUG_MODE = False          # Enable debug mode
VERBOSE_TRAINING = 1        # Verbosity level for training (0, 1, 2)
PLOT_INTERMEDIATE_RESULTS = False  # Plot results during training

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

def validate_config():
    """
    Validate configuration parameters
    """
    errors = []
    
    # Check file paths
    if not os.path.exists(os.path.dirname(IMAGE_FILE)):
        errors.append(f"Image file directory does not exist: {os.path.dirname(IMAGE_FILE)}")
    
    if not os.path.exists(os.path.dirname(KILOSORT_FILE)):
        errors.append(f"Kilosort file directory does not exist: {os.path.dirname(KILOSORT_FILE)}")
    
    # Check ratios sum to 1
    if abs(TRAIN_RATIO + VALIDATION_RATIO + TEST_RATIO - 1.0) > 1e-6:
        errors.append("Train, validation, and test ratios must sum to 1.0")
    
    # Check positive parameters
    if LEARNING_RATE <= 0:
        errors.append("Learning rate must be positive")
    
    if BATCH_SIZE <= 0:
        errors.append("Batch size must be positive")
    
    if FILTER_SIZE <= 0:
        errors.append("Filter size must be positive")
    
    # Check image size
    if len(EXPECTED_IMAGE_SIZE) != 2:
        errors.append("Expected image size must be a tuple of length 2")
    
    if any(dim <= 0 for dim in EXPECTED_IMAGE_SIZE):
        errors.append("Image dimensions must be positive")
    
    # Create results directory if it doesn't exist
    os.makedirs(RESULTS_DIR, exist_ok=True)
    
    if errors:
        raise ValueError("Configuration errors:\n" + "\n".join(errors))
    
    return True

def print_config():
    """
    Print current configuration
    """
    print("=== CONFIGURATION ===")
    print(f"Image file: {IMAGE_FILE}")
    print(f"Kilosort file: {KILOSORT_FILE}")
    print(f"Neuron ID: {NEURON_ID}")
    print(f"Expected image size: {EXPECTED_IMAGE_SIZE}")
    print(f"Filter size: {FILTER_SIZE}")
    print(f"Learning rate: {LEARNING_RATE}")
    print(f"Batch size: {BATCH_SIZE}")
    print(f"Max epochs: {MAX_EPOCHS}")
    print(f"Results directory: {RESULTS_DIR}")
    print("=" * 20)

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

def get_model_filename(pass_number, neuron_id=None):
    """
    Generate model filename
    """
    if neuron_id is not None:
        return os.path.join(RESULTS_DIR, f'best_model_pass{pass_number}_neuron{neuron_id}.h5')
    else:
        return os.path.join(RESULTS_DIR, f'best_model_pass{pass_number}.h5')

def get_results_filename(neuron_id=None):
    """
    Generate results filename
    """
    if neuron_id is not None:
        return os.path.join(RESULTS_DIR, f'analysis_results_neuron{neuron_id}.mat')
    else:
        return os.path.join(RESULTS_DIR, 'analysis_results.mat')

def get_figure_filename(figure_name, neuron_id=None):
    """
    Generate figure filename
    """
    if neuron_id is not None:
        return os.path.join(RESULTS_DIR, f'{figure_name}_neuron{neuron_id}.{FIGURE_FORMAT}')
    else:
        return os.path.join(RESULTS_DIR, f'{figure_name}.{FIGURE_FORMAT}')

# Validate configuration when imported
if __name__ == "__main__":
    validate_config()
    print_config()
    print("Configuration is valid!")
